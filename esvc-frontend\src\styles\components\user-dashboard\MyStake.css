/* My Stake Content */
.my-stake-content {
  position: relative;
  z-index: 2;
}

/* Additional gradient blur under body */
.my-stake-content::before {
  content: '';
  position: absolute;
  width: 316px;
  height: 316px;
  left: 0px;
  top: 400px;
  background: #CC6754;
  opacity: 0.3;
  filter: blur(132.218px);
  z-index: -1;
}

.my-stake-content::after {
  content: '';
  position: absolute;
  width: 316px;
  height: 316px;
  right: 0px;
  top: 600px;
  background: #CC6754;
  opacity: 0.3;
  filter: blur(132.218px);
  z-index: -1;
}

/* User Header */
.user-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 60px 40px 40px;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

/* Desktop: Position controls outside greeting */
@media (min-width: 769px) {
  .user-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .user-greeting {
    flex: 1;
    position: relative;
  }

  .header-controls {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
  }
}

/* User Header Background - Override general layout */
.my-stake-container .user-header::before {
  content: '';
  position: absolute;
  top: -120px;
  left: -50vw;
  right: -50vw;
  height: 360px !important; /* Calculated to cover greeting + controls */
  background: #260D08;
  z-index: -1;
}

/* Mobile: Adjust red background height */
@media (max-width: 768px) {
  .my-stake-container .user-header::before {
    top: -120px;
    bottom: -300px; /* Extend down much further to cover all mobile content */
    height: 385px !important; /* Let bottom positioning control the height */
  }
}

/* Gradient Blur under greeting */
.user-header::after {
  content: '';
  position: absolute;
  width: 316px;
  height: 316px;
  left: 0px;
  top: 200px;
  background: #CC6754;
  opacity: 0.3;
  filter: blur(132.218px);
  z-index: -1;
}

/* User Greeting */
.user-greeting {
  position: relative;
}

.greeting-text {
  font-family: 'Montserrat', sans-serif;
  font-size: 48px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.greeting-subtitle {
  font-family: 'Montserrat', sans-serif;
  font-size: 18px;
  color: #CCCCCC;
  margin: 0 0 32px 0;
  font-weight: 400;
}

.header-controls {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

/* Balance Toggle */
.balance-toggle {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toggle-label {
  font-size: 14px;
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
}

.toggle-switch {
  position: relative;
  width: 48px;
  height: 24px;
  cursor: pointer;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #404040;
  border-radius: 24px;
  transition: 0.3s;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background: #FFFFFF;
  border-radius: 50%;
  transition: 0.3s;
}

.toggle-switch input:checked + .toggle-slider {
  background: #BF4129;
}

.toggle-switch input:checked + .toggle-slider:before {
  transform: translateX(24px);
}

/* Stake Button */
.stake-esvc-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #BF4129;
  color: #FFFFFF;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stake-esvc-btn:hover {
  background: #A63622;
  transform: translateY(-1px);
}

.btn-icon {
  width: 20px;
  height: 20px;
}

/* Dashboard Layout */
.dashboard-layout {
  display: flex;
  gap: 32px;
  align-items: flex-start;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
  margin-top: 10px; /* Reduced desktop spacing between red section and navbar */
}

/* Dashboard Content Wrapper - Borrowed from UserOverview */
.dashboard-content-wrapper {
  display: flex;
  gap: 40px;
  width: 100%;
}

.dashboard-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Wallet Selector Section */
.wallet-selector-section {
  width: 100%;
}

.wallet-selector-card {
  background: #1D1104;
  border-radius: 16px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 24px;
}

.wallet-dropdown {
  width: 100%;
  background: #262626;
  border: 1px solid #404040;
  border-radius: 8px;
  padding: 12px 16px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  cursor: pointer;
  outline: none;
}

.wallet-dropdown:focus {
  border-color: #BF4129;
}

/* Stake Details Section */
.stake-details-section {
  width: 100%;
}

.stake-details-card {
  background: #1D1104;
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Stake Amount Section */
.stake-amount-section {
  text-align: left;
  margin-bottom: 24px;
}

.amount-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: #CCCCCC;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

.amount-value {
  font-family: 'Montserrat', sans-serif;
  font-size: 32px;
  font-weight: 700;
  color: #FFFFFF;
  margin-bottom: 4px;
}

.amount-unit {
  font-size: 24px;
  color: #CCCCCC;
  margin-left: 4px;
}

.amount-usd {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #CCCCCC;
  font-weight: 500;
}

/* ROI Progress Section */
.roi-progress-section {
  margin-bottom: 24px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 12px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50 0%, #8BC34A 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.roi-labels {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.roi-earned {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #4CAF50;
  font-weight: 600;
}

.roi-expected {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #CCCCCC;
  font-weight: 500;
}

/* Withdraw Section */
.withdraw-section {
  margin-bottom: 24px;
}

.withdraw-earned-btn {
  background: #c6741b;
  color: #FFFFFF;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.withdraw-earned-btn:hover {
  background: #b8661a;
  transform: translateY(-1px);
}

/* Stake Info Grid */
.stake-info-grid {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #CCCCCC;
  font-weight: 500;
}

.info-value {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  color: #FFFFFF;
  font-weight: 600;
}

.status-active {
  color: #22C55E;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .user-header {
    padding: 80px 20px 20px;
    flex-direction: column;
    gap: 24px;
  }

  .user-header::before {
    height: 480px; /* Extended further to fully contain header controls */
  }

  .user-greeting {
    width: 100%;
  }

  .header-controls {
    position: static;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    padding: 0;
  }

  .balance-toggle {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;
    min-width: auto;
  }

  .stake-esvc-btn {
    width: auto;
    min-width: 140px;
    justify-content: center;
    padding: 12px 16px;
    font-size: 14px;
    margin: 0;
  }

  .dashboard-layout {
    flex-direction: column;
    gap: 20px;
    padding: 0 20px;
    margin-top: -100px !important; /* Further reduced spacing for better mobile layout */
  }

  .dashboard-content-wrapper {
    flex-direction: column;
    gap: 16px;
  }

  .stake-header {
    flex-direction: column;
    align-items: stretch;
    gap: 20px;
  }

  .stake-stats {
    justify-content: space-around;
    gap: 16px;
    flex-wrap: wrap;
  }

  .stake-info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .wallet-selector {
    width: 100%;
    margin-bottom: 20px;
  }

  .stake-amount-card {
    padding: 20px;
    margin-bottom: 20px;
  }

  .stake-amount {
    font-size: 28px;
  }

  .stake-amount-label {
    font-size: 14px;
  }

  .stake-value {
    font-size: 14px;
  }

  .roi-section {
    padding: 20px;
  }

  .roi-stats {
    flex-direction: column;
    gap: 16px;
  }

  .roi-stat {
    text-align: center;
  }

  .unstake-section {
    padding: 20px;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .greeting-text {
    font-size: 32px;
  }

  .greeting-subtitle {
    font-size: 16px;
  }

  /* Prevent card overflow on mobile */
  .stake-amount-card,
  .roi-section,
  .unstake-section {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow: hidden;
  }

  .stake-amount-card {
    min-height: auto;
  }

  .roi-progress-bar {
    width: 100%;
    max-width: 100%;
  }

  .withdraw-btn {
    width: 100%;
    max-width: 200px;
    margin: 0 auto;
    display: block;
  }

  /* Fix container widths */
  .user-transactions-container,
  .dashboard-layout > * {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }
}
