/* User Transactions Container */
.user-transactions-container {
  width: 100%;
  padding: 0;
}

/* User Header Background - Override general layout */
.user-transactions-container .user-header::before {
  content: '';
  position: absolute;
  top: -120px;
  left: -50vw;
  right: -50vw;
  height: 360px !important; /* Calculated to cover greeting + controls */
  background: #260D08;
  z-index: -1;
}

/* Dashboard Layout */
.user-transactions-container .dashboard-layout {
  display: flex;
  gap: 32px;
  align-items: flex-start;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  margin-top: 10px; /* Reduced desktop spacing between red section and navbar */
}

/* Dashboard Content Wrapper - Borrowed from UserOverview */
.dashboard-content-wrapper {
  display: flex;
  gap: 40px;
  width: 100%;
}

.user-transactions-container .transactions-main-content {
  flex: 1;
  max-width: calc(100% - 280px - 32px); /* Account for sidebar width and gap */
}

/* Wallet Selector */
.wallet-selector {
  margin-bottom: 24px;
}

.wallet-select {
  width: 100%;
  max-width: 400px;
  padding: 16px 20px;
  background: #262626;
  border: 1px solid #404040;
  border-radius: 12px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  outline: none;
}

.wallet-select:focus {
  border-color: #BF4129;
}

/* Transaction Tabs */
.transaction-tabs {
  display: flex;
  gap: 0;
  margin-bottom: 24px;
  background: #262626;
  border-radius: 12px;
  padding: 4px;
  width: fit-content;
}

.transaction-tab {
  padding: 12px 24px;
  background: none;
  border: none;
  border-radius: 8px;
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.transaction-tab:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #FFFFFF;
}

.transaction-tab.active {
  background: #404040;
  color: #FFFFFF;
}

/* Transactions Controls */
.transactions-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 20px;
}

.time-filter {
  display: flex;
  align-items: center;
}

.filter-select {
  padding: 12px 16px;
  background: #262626;
  border: 1px solid #404040;
  border-radius: 8px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  cursor: pointer;
  outline: none;
  min-width: 140px;
}

.filter-select:focus {
  border-color: #BF4129;
}

.total-value-card {
  background: #1A1A1A;
  border: 1px solid #333333;
  border-radius: 12px;
  padding: 16px 20px;
  text-align: center;
  min-width: 200px;
}

.total-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  color: #888888;
  font-weight: 500;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.total-amount {
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  color: #FFFFFF;
  font-weight: 700;
}

/* Transactions List */
.transactions-list {
  background: #1A1A1A;
  border: 1px solid #333333;
  border-radius: 16px;
  padding: 0;
  margin-bottom: 24px;
  overflow: hidden;
}

.transaction-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px 24px;
  border-bottom: 1px solid #333333;
  transition: background 0.2s ease;
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-item:hover {
  background: rgba(255, 255, 255, 0.02);
}

.transaction-number {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  color: #888888;
  font-weight: 600;
  min-width: 30px;
}

.transaction-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  background: #262626;
  display: flex;
  align-items: center;
  justify-content: center;
}

.transaction-icon img {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.transaction-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.transaction-type {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  color: #FFFFFF;
  font-weight: 600;
}

.transaction-info {
  display: flex;
  gap: 8px;
  align-items: center;
}

.transaction-duration,
.transaction-from,
.transaction-to {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #888888;
  font-weight: 500;
}

.transaction-amount-time {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.transaction-amount {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  color: #FFFFFF;
  font-weight: 600;
}

.transaction-time {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #888888;
  font-weight: 500;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  margin-top: 24px;
}

.pagination-center {
  display: flex;
  gap: 8px;
  align-items: center;
}

.pagination-btn {
  padding: 12px 16px;
  background: #262626;
  border: 1px solid #404040;
  border-radius: 8px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-btn:hover:not(.disabled) {
  background: #404040;
  border-color: #525252;
}

.pagination-btn.active {
  background: #BF4129;
  border-color: #BF4129;
  color: #FFFFFF;
}

.pagination-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-ellipsis {
  color: #888888;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  padding: 0 8px;
}

/* Mobile: Adjust red background height */
@media (max-width: 768px) {
  .user-transactions-container .user-header::before {
    top: -120px;
    bottom: -300px; /* Extend down much further to cover all mobile content */
    height: 385px !important; /* Let bottom positioning control the height */
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .user-transactions-container {
    padding: 0;
  }

  /* Mobile user header padding to match other pages */
  .user-transactions-container .user-header {
    padding: 100px 20px 40px; /* Increased top padding to shift texts down */
    flex-direction: column;
    gap: 24px;
  }

  .user-transactions-container .dashboard-layout {
    flex-direction: column;
    gap: 20px;
    padding: 0 20px;
  }

  .dashboard-content-wrapper {
    flex-direction: column;
    gap: 16px;
    margin-top: 40px; /* Reduced spacing to shift navbar up */
  }

  /* Mobile Sidenav positioning to match other pages */
  .user-transactions-container .user-sidenav-container {
    margin-top: 20px; /* Add space above sidenav */
    order: 1;
  }

  .user-transactions-container .transactions-main-content {
    max-width: 100%;
    order: 2;
  }

  .wallet-selector {
    margin-bottom: 20px;
  }

  .wallet-select {
    max-width: 100%;
    font-size: 14px;
    padding: 14px 16px;
  }

  .transaction-tabs {
    width: 100%;
    justify-content: center;
  }

  .transaction-tab {
    flex: 1;
    font-size: 14px;
    padding: 10px 16px;
    min-width: auto;
  }

  .transactions-controls {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .total-value-card {
    min-width: auto;
    width: 100%;
  }

  .filter-select {
    width: 100%;
    min-width: auto;
  }

  .transaction-item {
    padding: 16px 20px;
    gap: 12px;
  }

  .transaction-number {
    font-size: 14px;
    min-width: 25px;
  }

  .transaction-icon {
    width: 36px;
    height: 36px;
  }

  .transaction-icon img {
    width: 28px;
    height: 28px;
  }

  .transaction-type {
    font-size: 14px;
  }

  .transaction-duration,
  .transaction-from,
  .transaction-to {
    font-size: 12px;
  }

  .transaction-amount {
    font-size: 14px;
  }

  .transaction-time {
    font-size: 12px;
  }

  .pagination {
    flex-direction: column;
    gap: 12px;
  }

  .pagination-center {
    order: 1;
  }

  .pagination-btn {
    padding: 10px 14px;
    font-size: 12px;
    min-width: 40px;
    height: 40px;
  }
}
